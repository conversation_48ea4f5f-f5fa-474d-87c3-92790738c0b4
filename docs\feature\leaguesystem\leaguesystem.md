### 🎯 Objetivo

Aumentar o engajamento, a frequência de uso e o senso de pertencimento por meio da criação de **grupos de competição entre amigos**.  
Inspirado na mecânica do app **Gymrats**, o objetivo é criar o hábito de registrar a rotina financeira **todos os dias**, junto com quem o usuário conhece.

---

### ❓ Como funciona

#### ✅ Criação de Grupos (Ligas)

- O usuário pode:
    - Criar uma nova **Liga** com nome, imagem e descrição personalizada
    - Gerar um **código de convite** ou **link compartilhável**
    - Convidar amigos para entrar na sua liga e competir
    - Compartilhar links de convite no formato: `https://dinbora.com.br/join?code=ABCDE` (produção) ou `http://localhost:8080/join?code=ABCDE` (desenvolvimento)
- Cada liga é **um grupo privado de competição** com um **ranking próprio**

#### 🔁 Mecânica da Competição

- **Investida = 1 ponto diário**  
    Cada vez que o usuário preenche sua [[Ligas (Planilha Financeira)]]no dia, ele marca um ponto chamado de **“Investida”**
- **Contagem por Ciclo (Trimestre)**  
    A liga contabiliza quem teve **mais dias preenchidos** no período de 3 meses
- **Consistência vence**  
    Quem for mais consistente — e não necessariamente quem “fizer mais” — lidera a liga

---

### 🧩 Elementos da Liga

- **Nome da Liga**
- **Foto personalizada da Liga**
- **Ranking interno** com:
    - Nome do usuário ([[Dados cadastrais]])
    - Avatar ([[Escolha de avatar]])
    - Dias de investida acumulados
    - Posição atual na competição
    - Exibição do [[Cartão de Visitas]] ao clicar em um membro
- **Feed ou mural** da liga (futuro)  
    → Para comentar, incentivar, celebrar conquistas

---

### 🗂️ Integrações e Destino das Informações

- [[Ligas (Planilha Financeira)]] → valida se o dia foi registrado
- [[Cartão de Visitas]] → exibição no ranking

---

### 🎮 Mecânica Base (Referência: GymRats)

- **Crie seu grupo** com amigos ou colegas
- **Desafie eles** a serem consistentes
- **Marque presença diariamente** para manter sua posição
- **Ganha quem for mais disciplinado no trimestre**

#### Especificação GymRats para referência

- Desafios em Grupo
    - Descrição: Permitir que os usuários criem ou participem de desafios de fitness com participantes ilimitados.
    - Funcionalidade:
        - Criar desafio: Definir nome, duração e regras de pontuação.
        - Entrar no desafio: Através de link de convite ou código.
        - Suporte a equipes: Formar equipes dentro dos desafios para competições em grupo.
        - Componentes de UI: Formulário de criação de desafio, lista de participantes, interface de gerenciamento de equipes.

- Sistema de Pontuação Flexível
    - Descrição: Permitir pontuação personalizável baseada em várias métricas.
    - Funcionalidade:
        - Métricas predefinidas: Treinos, milhas, minutos, calorias, passos.
        - Pontos personalizados: Definir “Pontos de Esforço” baseados em atividade.
        - Quadro de líderes: Exibir classificações com base na métrica escolhida.
        - Componentes de UI: Editor de regras de pontuação, visualização do quadro de líderes.

Isso gera:

- Senso de compromisso coletivo
- Estímulo diário baseado em leve competição entre conhecidos
- Recompensas simbólicas e sociais (ex: categoria na liga, avatares melhores, posição no grupo)

---

### 🎨 Observações de UI/UX

- Destaque para botão: **Criar Liga**
- Layout em formato de podium ou lista dinâmica
- Atualização diária automática
- Feedback visual (fogo, confete, selo de consistência) a cada investida
- Avatares grandes + apelidos + emojis opcionais para gerar familiaridade

---

### 🔧 Implementação Técnica

#### 📋 API Endpoints

**Gerar Link de Convite**
- **Endpoint**: `GET /v2/leagues/me/:leagueId/invite`
- **Autenticação**: Requerida (usuário deve ser membro da liga)
- **Resposta**:
```json
{
  "inviteCode": "ABCDE12345",
  "inviteUrl": "https://dinbora.com.br/join?code=ABCDE12345"
}
```

**Entrar na Liga**
- **Endpoint**: `POST /v2/leagues/join`
- **Autenticação**: Requerida
- **Body**:
```json
{
  "inviteCode": "ABCDE12345"
}
```

#### ⚙️ Configuração

- **Variável de Ambiente**: `APP_URL`
  - Produção: `https://dinbora.com.br`
  - Teste: `https://test.dinbora.com.br`
  - Desenvolvimento (fallback quando vazio): `http://localhost:8080`

#### 🔐 Segurança

- Códigos de convite são gerados como strings hexadecimais de 16 caracteres
- Apenas membros da liga podem gerar links de convite
- Links não expiram (podem ser reutilizados)
- Usuários não podem entrar na mesma liga duas vezes
