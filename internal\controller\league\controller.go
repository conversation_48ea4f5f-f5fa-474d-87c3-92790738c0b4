package league

import (
	"context"
	"net/http"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/api/middlewares"
	"github.com/dsoplabs/dinbora-backend/internal/api/token"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/service/league"
	_user "github.com/dsoplabs/dinbora-backend/internal/service/user" // Added UserService import
	"github.com/labstack/echo/v4"
)

type Controller interface {
	// Routes
	RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group)

	// CRUD
	CreateLeague() echo.HandlerFunc
	FindLeague() echo.HandlerFunc
	FindAllLeagues() echo.HandlerFunc
	PatchLeague() echo.HandlerFunc
	DeleteLeague() echo.HandlerFunc

	// Membership
	InviteLeague() echo.HandlerFunc
	JoinLeague() echo.HandlerFunc
	LeaveLeague() echo.HandlerFunc

	// Gameplay
	StartNewSeason() echo.HandlerFunc
	FindLeagueRanking() echo.HandlerFunc

	// Cards
	FindLeagueCard() echo.HandlerFunc
	FindAllLeaguesCards() echo.HandlerFunc
}

type controller struct {
	Service     league.Service
	UserService _user.Service // Added UserService dependency
}

func New(service league.Service, userService _user.Service) Controller { // Added userService parameter
	return &controller{
		Service:     service,
		UserService: userService,
	}
}

// RegisterRoutes connects the league routes to the Echo router group
func (lc *controller) RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group) {
	leaguesGroup := currentGroup.Group("/leagues", middlewares.AuthGuard())

	leaguesGroup.POST("", lc.CreateLeague())
	leaguesGroup.GET("/me/:leagueId", lc.FindLeague()) // Changed to /me/:leagueId to indicate user-specific access
	leaguesGroup.GET("/me", lc.FindAllLeagues())
	leaguesGroup.PATCH("/me/:leagueId", lc.PatchLeague())
	leaguesGroup.DELETE("/me/:leagueId", lc.DeleteLeague())

	leaguesGroup.GET("/me/:leagueId/invite", lc.InviteLeague())
	leaguesGroup.POST("/join", lc.JoinLeague())
	leaguesGroup.POST("/leave", lc.LeaveLeague())

	//leaguesGroup.POST("/me/:leagueId/season", lc.StartNewSeason()) // Evaluate the need for start a season or not
	leaguesGroup.GET("/me/:leagueId/ranking", lc.FindLeagueRanking())

	// Card endpoints
	leaguesGroup.GET("/me/:leagueId/cards", lc.FindLeagueCard())
	leaguesGroup.GET("/me/cards", lc.FindAllLeaguesCards())

	// Note: RecordTransactionStreak is not an explicit endpoint.
	// It's triggered by financialsheet.CreateTransaction.
}

// DTOs for request/response bodies

type CreateLeagueRequest struct {
	Name      string    `json:"name" validate:"required,min=3,max=50"`
	ImageURL  string    `json:"imageUrl,omitempty" validate:"omitempty,url"`
	StartDate time.Time `json:"startDate" validate:"required"`
	EndDate   time.Time `json:"endDate" validate:"required"` // Add validation: EndDate after StartDate
	// UserName string `json:"userName" validate:"required,min=1"` // Removed: Will be fetched from UserService
}

type PatchRequest struct {
	Name     *string `json:"name,omitempty" validate:"omitempty,min=3,max=50"`
	ImageURL *string `json:"imageUrl,omitempty" validate:"omitempty,url"`
}

type JoinLeagueRequest struct {
	InviteCode string `json:"inviteCode" validate:"required"`
	// UserName   string `json:"userName" validate:"required,min=1"`           // Removed: Will be fetched from UserService
	// AvatarURL  string `json:"avatarUrl,omitempty" validate:"omitempty,url"` // Removed: Will be fetched from UserService, and field is PhotoURL
}

type LeaveLeagueRequest struct {
	LeagueID string `json:"leagueId" validate:"required"`
}

type StartSeasonRequest struct {
	Name      string    `json:"name" validate:"required,min=3,max=50"`
	StartDate time.Time `json:"startDate" validate:"required"`
	EndDate   time.Time `json:"endDate" validate:"required"` // Add validation: EndDate after StartDate
}

// CRUD
func (lc *controller) CreateLeague() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		var req CreateLeagueRequest // Use the existing body struct

		if err := c.Bind(&req); err != nil {
			return errors.New(errors.Controller, "invalid_create_league_request_body", errors.BadRequest, err)
		}

		if err := c.Validate(&req); err != nil {
			// errors.Validation is a Kind, so we create a DomainError
			return errors.New(errors.Controller, "create_league_validation_failed", errors.Validation, err)
		}

		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}
		userID := userToken.Uid

		// Fetch user details from UserService
		user, err := lc.UserService.Find(ctx, userID) // Changed to use Find method
		if err != nil {
			// Consider how to handle this error: if user not found, can't create league with owner name
			return errors.New(errors.Controller, "failed_to_fetch_owner_profile", errors.Internal, err)
		}
		if user == nil { // Defensive check, though Find should return NotFound error
			return errors.New(errors.Controller, "owner_profile_not_found", errors.NotFound, nil)
		}

		// TODO: Add custom validation for StartDate < EndDate in the request
		if req.StartDate.After(req.EndDate) {
			return errors.New(errors.Controller, "create_league_validation_failed", errors.Validation, nil)
		}

		createdLeague, err := lc.Service.CreateLeague(ctx, userID, user.Name, user.PhotoURL, req.Name, req.ImageURL, req.StartDate, req.EndDate)
		if err != nil {
			return err
		}
		return c.JSON(http.StatusCreated, createdLeague)
	}
}

func (lc *controller) FindLeague() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		leagueID := c.Param("leagueId")

		// Get user ID from token
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}
		userID := userToken.Uid

		// Pass userID to service for access control
		l, err := lc.Service.FindLeague(ctx, leagueID, userID)
		if err != nil {
			return err // Return DomainError directly
		}
		return c.JSON(http.StatusOK, l)
	}
}

func (lc *controller) FindAllLeagues() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}
		userID := userToken.Uid

		userLeagues, serviceErr := lc.Service.FindAllLeagues(ctx, userID)
		if serviceErr != nil {
			return serviceErr // Return DomainError directly
		}
		return c.JSON(http.StatusOK, userLeagues)
	}
}

func (lc *controller) PatchLeague() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}
		userID := userToken.Uid
		leagueID := c.Param("leagueId")

		var req PatchRequest
		if err := c.Bind(&req); err != nil {
			return errors.New(errors.Controller, "invalid_update_league_request_body", errors.BadRequest, err)
		}
		if err := c.Validate(&req); err != nil {
			return errors.New(errors.Controller, "update_league_validation_failed", errors.Validation, err)
		}

		updatedLeague, serviceErr := lc.Service.PatchLeague(ctx, leagueID, userID, req.Name, req.ImageURL)
		if serviceErr != nil {
			return serviceErr // Return DomainError directly
		}
		return c.JSON(http.StatusOK, updatedLeague)
	}
}

func (lc *controller) DeleteLeague() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}
		userID := userToken.Uid
		leagueID := c.Param("leagueId")

		serviceErr := lc.Service.DeleteLeague(ctx, leagueID, userID)
		if serviceErr != nil {
			return serviceErr // Return DomainError directly
		}
		return c.NoContent(http.StatusNoContent)
	}
}

// Membership
func (lc *controller) InviteLeague() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}
		userID := userToken.Uid
		leagueID := c.Param("leagueId")

		league, serviceErr := lc.Service.InviteLeague(ctx, leagueID, userID)
		if serviceErr != nil {
			return serviceErr // Return DomainError directly
		}
		return c.JSON(http.StatusOK, echo.Map{"inviteCode": league.InviteCode})
	}
}

func (lc *controller) JoinLeague() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}
		userID := userToken.Uid

		var req JoinLeagueRequest
		if err := c.Bind(&req); err != nil {
			return errors.New(errors.Controller, "invalid_join_league_request_body", errors.BadRequest, err)
		}
		if err := c.Validate(&req); err != nil {
			return errors.New(errors.Controller, "join_league_validation_failed", errors.Validation, err)
		}

		// Fetch user details from UserService
		user, err := lc.UserService.Find(ctx, userID) // Changed to use Find method
		if err != nil {
			// Consider how to handle this error: if user not found, can't join league with user name/photo
			return errors.New(errors.Controller, "failed_to_fetch_user_profile_for_join", errors.Internal, err)
		}
		if user == nil { // Defensive check
			return errors.New(errors.Controller, "user_profile_not_found_for_join", errors.NotFound, nil)
		}

		joinedLeague, err := lc.Service.JoinLeague(ctx, userID, user.Name, user.PhotoURL, req.InviteCode)
		if err != nil {
			return err // Return DomainError directly
		}
		return c.JSON(http.StatusOK, joinedLeague)
	}
}

func (lc *controller) LeaveLeague() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}
		userID := userToken.Uid

		var req LeaveLeagueRequest
		if err := c.Bind(&req); err != nil {
			return errors.New(errors.Controller, "invalid_leave_league_request_body", errors.BadRequest, err)
		}
		if err := c.Validate(&req); err != nil {
			return errors.New(errors.Controller, "leave_league_validation_failed", errors.Validation, err)
		}

		if err := lc.Service.LeaveLeague(ctx, req.LeagueID, userID); err != nil {
			return err // Return DomainError directly
		}
		return c.NoContent(http.StatusNoContent)
	}
}

// Gameplay
func (lc *controller) StartNewSeason() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}
		userID := userToken.Uid
		leagueID := c.Param("leagueId")

		var req StartSeasonRequest
		if err := c.Bind(&req); err != nil {
			return errors.New(errors.Controller, "invalid_start_season_request_body", errors.BadRequest, err)
		}
		// TODO: Add custom validation for StartDate < EndDate
		if err := c.Validate(&req); err != nil {
			return errors.New(errors.Controller, "start_season_validation_failed", errors.Validation, err)
		}

		updatedLeague, serviceErr := lc.Service.StartNewSeasonInLeague(ctx, leagueID, userID, req.Name, req.StartDate, req.EndDate)
		if serviceErr != nil {
			return serviceErr // Return DomainError directly
		}
		return c.JSON(http.StatusOK, updatedLeague)
	}
}

func (lc *controller) FindLeagueRanking() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		leagueID := c.Param("leagueId")

		// Get user ID from token
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}
		userID := userToken.Uid

		// Pass userID to service for access control
		ranking, err := lc.Service.FindLeagueRanking(ctx, leagueID, userID)
		if err != nil {
			return err // Return DomainError directly
		}
		return c.JSON(http.StatusOK, ranking)
	}
}

// Cards
func (lc *controller) FindLeagueCard() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		leagueID := c.Param("leagueId")

		// Get user ID from token
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}
		userID := userToken.Uid

		// Pass userID to service for access control
		card, err := lc.Service.FindLeagueCard(ctx, leagueID, userID)
		if err != nil {
			return err // Return DomainError directly
		}
		return c.JSON(http.StatusOK, card)
	}
}

func (lc *controller) FindAllLeaguesCards() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		// Get user ID from token
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}
		userID := userToken.Uid

		// Get all league cards for the user
		cards, err := lc.Service.FindAllLeaguesCards(ctx, userID)
		if err != nil {
			return err // Return DomainError directly
		}
		return c.JSON(http.StatusOK, cards)
	}
}
