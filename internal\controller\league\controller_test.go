package league

import (
	"os"
	"testing"
)

func TestGenerateInviteURL(t *testing.T) {
	tests := []struct {
		name        string
		appURL      string
		inviteCode  string
		expectedURL string
	}{
		{
			name:        "Production URL",
			appURL:      "https://dinbora.com.br",
			inviteCode:  "ABCDE12345",
			expectedURL: "https://dinbora.com.br/join?code=ABCDE12345",
		},
		{
			name:        "Development URL",
			appURL:      "http://localhost:3000",
			inviteCode:  "DEV123456",
			expectedURL: "http://localhost:3000/join?code=DEV123456",
		},
		{
			name:        "Empty APP_URL uses fallback",
			appURL:      "",
			inviteCode:  "FALLBACK123",
			expectedURL: "https://dinbora.com.br/join?code=FALLBACK123",
		},
		{
			name:        "Test environment URL",
			appURL:      "https://test.dinbora.com.br",
			inviteCode:  "TEST789",
			expectedURL: "https://test.dinbora.com.br/join?code=TEST789",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Set the environment variable for this test
			if tt.appURL != "" {
				os.Setenv("APP_URL", tt.appURL)
			} else {
				os.Unsetenv("APP_URL")
			}

			// Call the function
			result := generateInviteURL(tt.inviteCode)

			// Check the result
			if result != tt.expectedURL {
				t.Errorf("generateInviteURL() = %v, want %v", result, tt.expectedURL)
			}
		})
	}

	// Clean up environment variable
	os.Unsetenv("APP_URL")
}

func TestInviteLeagueResponse(t *testing.T) {
	response := InviteLeagueResponse{
		InviteCode: "ABCDE12345",
		InviteURL:  "https://dinbora.com.br/join?code=ABCDE12345",
	}

	if response.InviteCode != "ABCDE12345" {
		t.Errorf("Expected InviteCode to be 'ABCDE12345', got %s", response.InviteCode)
	}

	if response.InviteURL != "https://dinbora.com.br/join?code=ABCDE12345" {
		t.Errorf("Expected InviteURL to be 'https://dinbora.com.br/join?code=ABCDE12345', got %s", response.InviteURL)
	}
}
